#!/usr/bin/env php
<?php
# 关闭端口
foreach ([9501, 9502, 9503] as $port) {
    echo "Checking port $port...\n";

    // 获取进程ID
    $pids = shell_exec("lsof -i tcp:$port -t 2>/dev/null");

    if (empty($pids)) {
        echo "Port $port is not in use.\n";
        continue;
    }

    // 清理输出并验证PID
    $pids = trim($pids);
    $pidArray = explode("\n", $pids);

    foreach ($pidArray as $pid) {
        $pid = trim($pid);

        // 验证PID是否为数字
        if (!empty($pid) && is_numeric($pid)) {
            echo "Killing process $pid on port $port...\n";
            $result = shell_exec("kill -9 $pid 2>&1");

            if ($result === null || empty(trim($result))) {
                echo "Process $pid killed successfully.\n";
            } else {
                echo "Error killing process $pid: " . trim($result) . "\n";
            }
        } else if (!empty($pid)) {
            echo "Invalid PID format: '$pid' (skipping)\n";
        }
    }
}